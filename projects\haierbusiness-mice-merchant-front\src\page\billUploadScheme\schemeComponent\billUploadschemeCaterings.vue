<script setup lang="ts">
// 方案互动-用餐方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  CateringTypeConstant,
  CateringTimeTypeConstant,
  HotelDinnerTypeConstant,
  HaveDrinksTypeConstant,
  HotelsArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  demandHotels: {
    type: Array,
    default: [],
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
  isCateringStandardControl: {
    // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeCateringsEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

const priceTip = ref<string>('');

// 价格计算
const priceCalcFun = () => {
  const priceWriteList = newSchemeList.value.filter(
    (e) => e.billUnitPrice && e.billPersonNum && e.billUnitPrice >= 0 && e.billPersonNum >= 0,
  );
  subtotal.value = 0;

  priceWriteList.forEach((e) => {
    subtotal.value += e.billUnitPrice * e.billPersonNum;
  });

  emit('schemePriceEmit', { type: 'catering', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

watch(
  () => props.schemeItem,
  (newObj) => {

    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.caterings || [];

    if (props.isSchemeCache && props.schemeCacheItem && props.schemeCacheItem.caterings && props.schemeCacheItem.caterings.length > 0) {
      // 缓存 - 反显（只有当缓存数据不为空时才使用）
      newSchemeList.value = props.schemeCacheItem.caterings;

      // 修复缓存数据中的关键字段
      newSchemeList.value.forEach((e) => {
        // 从原始数据中找到对应项
        const originalItem = oldSchemeList.value.find((item: any) => item.miceDemandCateringId === e.miceDemandCateringId);

        if (originalItem) {
          // 修复 sourceId - 直接使用详情数据的 id
          if (!e.hasOwnProperty('sourceId') || e.sourceId === null) {
            const oldSourceId = e.sourceId;
            e.sourceId = originalItem.id; // 直接取详情里面的 id
          }

          // 修复 miceSchemeCateringId - 也使用详情数据的 id
          if (!e.miceSchemeCateringId || e.miceSchemeCateringId === undefined) {
            const oldMiceSchemeCateringId = e.miceSchemeCateringId;
            e.miceSchemeCateringId = originalItem.id; // 和 sourceId 使用相同的值
          }

          // 强制同步左边需求数据的 schemePersonNum 和 schemeUnitPrice（不能修改的字段）
          // 人数：强制使用左边需求数据，不能被修改
          if (originalItem.schemePersonNum !== undefined) {
            const oldPersonNum = e.schemePersonNum;
            e.schemePersonNum = originalItem.schemePersonNum;
          }

          // 单价：强制使用左边需求数据，不能被修改
          const correctUnitPrice = originalItem.schemeUnitPrice || originalItem.demandUnitPrice;
          if (correctUnitPrice !== undefined && correctUnitPrice !== null) {
            const oldUnitPrice = e.schemeUnitPrice;
            e.schemeUnitPrice = correctUnitPrice; // 强制使用左边的值
          }

          // 同步更新 miceSchemeHotelId（确保使用最新的酒店关联）
          if (originalItem.miceSchemeHotelId && originalItem.miceSchemeHotelId !== e.miceSchemeHotelId) {
            const oldHotelId = e.miceSchemeHotelId;
            e.miceSchemeHotelId = originalItem.miceSchemeHotelId;
          }
        }
      });

      // 修复完成后，触发价格重新计算和数据保存
      nextTick(() => {
        priceCalcFun();
        cateringTempSave();
      });
    } else {
      const demandData = JSON.parse(JSON.stringify(newObj))?.caterings || [];
      if (demandData.length === 0) {
        newSchemeList.value = [];
      } else {
        newSchemeList.value = demandData.map((e) => {
          return {
            // 基础字段
            tempSchemeHotelId: props.hotels && props.hotels.length === 1 ? props.hotels[0].tempId : null,
            miceDemandPushHotelId:
              props.hotels && props.hotels.length === 1 ? props.hotels[0].miceDemandPushHotelId : null,
            miceDemandHotelId: e.miceDemandHotelId,
            miceDemandCateringId: e.id,
            miceSchemeHotelId: e.miceSchemeHotelId,
            miceSchemeCateringId: Date.now() + Math.random(), // 方案用餐id

            // 日期和基本信息
            demandDate: e.demandDate,
            isInsideHotel: e.isInsideHotel,
            cateringType: e.cateringType,
            cateringTime: e.cateringTime,

            // 人数
            schemePersonNum: e.personNum,
            billPersonNum: e.personNum, // 账单人数，默认等于方案人数

            // 酒水和描述
            isIncludeDrinks: e.isIncludeDrinks,
            description: e.description,

            // 价格相关
            demandUnitPrice: e.demandUnitPrice,
            schemeUnitPrice: props.isCateringStandardControl === '1' ? e.demandUnitPrice : (e.schemeUnitPrice || e.demandUnitPrice),
            billUnitPrice: null, // 账单单价

            // 其他字段
            sourceId: e.sourceId,
            invoiceTempId: undefined, // 临时id，用于关联发票表
            statementTempId: undefined, // 临时id，用于关联水单表

            // 控制字段（用于前端逻辑）
            min: props.isCateringStandardControl === '2' ? e.demandUnitPrice : 0,
            max: props.isCateringStandardControl === '3' ? e.demandUnitPrice : 999999.99,
          };
        });
      }
    }

    // 价格计算
    priceCalcFun();
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => props.isCateringStandardControl,
  () => {
    newSchemeList.value.forEach((e) => {
      // isCateringStandardControl - 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
      // 保持 schemeUnitPrice 的值，不要强制设置为 null
      if (props.isCateringStandardControl === '1') {
        e.schemeUnitPrice = e.schemeUnitPrice || e.demandUnitPrice;
      }
      // 注意：不再强制设置为 null，保持原有值

      e.min = props.isCateringStandardControl === '2' ? e.schemeUnitPrice || e.demandUnitPrice : 0;
      e.max = props.isCateringStandardControl === '3' ? e.schemeUnitPrice || e.demandUnitPrice : 999999.99;
    });

    priceTip.value =
      props.isCateringStandardControl === '1'
        ? '不可修改'
        : props.isCateringStandardControl === '2'
        ? '可以提高'
        : '可以降低';
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => [props.hotels, props.merchantType, props.demandHotels, newSchemeList.value],
  () => {
    newSchemeList.value.forEach((e) => {
      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1 ? props.hotels[0].tempId : e.miceSchemeHotelId || props.hotels[0].tempId;
        e.miceDemandPushHotelId = props.hotels[0].miceDemandPushHotelId;
      }

      // 修复：确保 miceSchemeHotelId 有正确的值
      // 从 demandHotels 中获取对应的酒店ID
      if (!e.miceSchemeHotelId && props.demandHotels && props.demandHotels.length > 0) {
        // 如果只有一个酒店，直接使用第一个酒店的ID
        if (props.demandHotels.length === 1) {
          e.miceSchemeHotelId = (props.demandHotels[0] as any).id;
        }
      }
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['餐饮提供方', '酒店位置', '用餐类型', '用餐时间', '人数', '餐标', '是否包含酒水', '备注'];

// 酒店名称
const hotelName = (hotelItem: any) => {
  let str = '-';

  props.demandHotels.forEach((e: any, index) => {
    // 修复：使用 miceSchemeHotelId 来匹配 demandHotels 中的 id
    if (e.id && e.id === hotelItem.miceSchemeHotelId) {
      str = `酒店${index + 1}(${
        e.cityName + e.districtName + '/' + (hotelLevelAllConstant.ofType(e.level)?.desc || '-')
      })`;
    }
  });

  return str;
};

const changePrice = (index: number) => {
  // 同步人数：将 schemePersonNum 的值同步到 billPersonNum
  if (newSchemeList.value[index]) {
    newSchemeList.value[index].billPersonNum = newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 暂存
const cateringTempSave = () => {

  emit('schemeCateringsEmit', {
    schemeCaterings: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const cateringSub = () => {
  let isCateringVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isCateringVerPassed === false) return;

    if (e.isInsideHotel && !e.tempSchemeHotelId) {
      message.error('请选择' + e.demandDate + '用餐' + (i + 1) + '餐饮提供方');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }

    if (!e.billUnitPrice) {
      message.error('请输入' + e.demandDate + '用餐' + (i + 1) + '账单单价');

      isCateringVerPassed = false;
      anchorJump('schemeCateringId' + e.demandDate + i);
      return;
    }
  });

  if (isCateringVerPassed) {
    cateringTempSave();
  }

  return isCateringVerPassed;
};

defineExpose({ cateringSub, cateringTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 用餐方案 -->
  <div class="scheme_catering">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>用餐需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用餐' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
                </template>
                {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.isInsideHotel === 1 ? hotelName(item) : '-' }}
                </template>
                {{ item.isInsideHotel === 1 ? hotelName(item) : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
                </template>
                {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
                </template>
                {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </template>
                {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 color_blue">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HaveDrinksTypeConstant.ofType(item.isIncludeDrinks)?.desc || '-' }}
                </template>
                {{ HaveDrinksTypeConstant.ofType(item.isIncludeDrinks)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div class="scheme_plan_price_value">
                {{ item.demandUnitPrice || '-' }}
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.demandUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.demandUnitPrice * item.schemePersonNum)
                    : '¥0.00'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.demandUnitPrice && item.schemePersonNum">
                {{ (item.schemePersonNum || 0) + '人*' + item.demandUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>用餐方案</span>
        </div>
        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '用餐' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
                </template>
                {{ HotelDinnerTypeConstant.ofType(item.isInsideHotel ? 1 : 0)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <!-- 酒店位置：不可修改，直接从左侧复制显示 -->
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.isInsideHotel === 1 ? hotelName(item) : '-' }}
                </template>
                {{ item.isInsideHotel === 1 ? hotelName(item) : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
                </template>
                {{ CateringTypeConstant.ofType(item.cateringType)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
                </template>
                {{ CateringTimeTypeConstant.ofType(item.cateringTime)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
              <div class="pl12" v-else>
                <!-- schemePersonNum 不可修改，只显示左边需求数据的值 -->
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人（来自需求数据，不可修改）' : '-' }}
                  </template>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12 color_blue">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/位（' + priceTip + '）' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ HaveDrinksTypeConstant.ofType(item.isIncludeDrinks)?.desc || '-' }}
                </template>
                {{ HaveDrinksTypeConstant.ofType(item.isIncludeDrinks)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <!-- 备注：不可修改，直接从左侧复制显示 -->
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12" :id="'schemeCateringId' + item.demandDate + idx">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="props.schemeType === 'biddingView' || props.schemeType === 'schemeView'"
              >
                {{ item.schemeUnitPrice }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.billUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.billUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="item.min"
                  :max="item.max"
                  :disabled="props.isCateringStandardControl === '1'"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.billUnitPrice && item.billPersonNum
                    ? formatNumberThousands(item.billUnitPrice * item.billPersonNum)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billUnitPrice">
                {{ (item.billPersonNum || 0) + '人*' + item.billUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_catering {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_catering.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  // 人数输入框样式，模仿备注字段
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }
  }

  .scheme_plan_value {
    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .p0 {
    padding: 0 !important;
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number-disabled) {
      background: none;
    }
  }

  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>
