<template>
  <div class="container standard-content">
    <h2 style="text-align: center">供应商行为规范</h2>
    <div>
      <h3>一、规范目的</h3>
      <p>
        为促进供应商在商务会展平台上的积极表现，提升合作效率与服务质量，特制定本行为规范。通过明确的行为准则对供应商进行动态管理，确保双方利益最大化。
      </p>
    </div>
    <div>
      <h3>二、行为评估机制</h3>
      <b>即时评估：</b>
      <p>当供应商在某次会议或活动中出现违规行为时，平台将立即启动评估程序。根据具体问题扣除相应分数或采取其他措施。</p>
      <b>定期评估：</b>
      <p>
        平台每季度对供应商的整体行为表现进行综合分析。长时间未参与活动或多次出现违规行为的供应商将被重点关注并采取相应措施。
      </p>
    </div>
    <div>
      <h3>三、行为规范及评分规则</h3>
      <p>供应商总分为100分，初始分数为100分。以下行为将影响供应商的得分：</p>
      <!-- 这里可根据后续补充细则，添加详细评分表格或列表 -->
    </div>

    <div v-for="(item, index) in list" :key="item.id">
      <h4>{{ index + 1 }}.{{ item.name }}</h4>
      <p v-for="detail in item.details" :key="detail">{{ detail }}</p>
    </div>
    <div>
      <h3>四、申诉机制</h3>
      <p>供应商如对评估结果有异议，可在结果公布后的3个工作日内联系会务负责人。平台将在7个工作日内给予回复。</p>
    </div>
    <div>
      <h3>五、附则</h3>
      <p>本行为规范自发布之日起生效，最终解释权归平台所有。平台有权根据实际情况调整规范内容，并提前通知供应商。</p>
    </div>
  </div>
  <!-- 通知弹框逻辑 -->
  <!-- <div v-if="noticeVisible">
    <notice @ok="handleShow" @cancel="handlecancel"></notice>
  </div> -->
</template>

<script lang="ts" setup>
import { fileApi, serviceExamApi, miceBidManServiceProviderApi, schemeApi } from '@haierbusiness-front/apis';
import { ref, reactive, onMounted, useAttrs, inject, computed, provide } from 'vue';
//通知弹框逻辑
import notice from '../component/Modal.vue';
const list = ref([]);
const getList = async () => {
  const res = await schemeApi.getMerchantByUser({});
  console.log(res);
  const res1 = await serviceExamApi.getExamItem({
    type: res.merchantType,
    state: 10,
    pageNum: 1,
    pageSize: 9999,
  });
  const res2 = await serviceExamApi.getExamItem({
    type: 6,
    state: 10,
    pageNum: 1,
    pageSize: 9999,
  });
  res1.records.concat(res2.records).forEach((item) => {
    let ifHas = false;
    list.value.forEach((item1) => {
      if (item1.name === item.name) {
        ifHas = true;
        item1.details.push(item.detail);
      }
    });
    if (!ifHas) {
      list.value.push({
        name: item.name,
        details: [item.detail],
      });
    }
  });
  // list.value = res1.records;
};
//通知弹框逻辑
const noticeVisible = ref(false);
//
const handleShow = () => {
  noticeVisible.value = false;
  console.log(noticeVisible.value);
};
const handlecancel = () => {
  noticeVisible.value = false;
};
onMounted(() => {
  getList();
  //通知弹框
  const showValue = sessionStorage.getItem('Show');
  if (showValue === null) {
    sessionStorage.setItem('Show', JSON.stringify(false));
    noticeVisible.value = true;
  } else {
    noticeVisible.value = JSON.parse(showValue);
  }
});
</script>

<style scoped>
.standard-content {
  /* max-width: 800px; */
  /* margin: 40px auto; */
  background: #fff;
  padding: 32px 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  font-size: 16px;
  color: #222;
}

.standard-content h2 {
  font-weight: bold;
  margin-bottom: 24px;
}

.standard-content h4 {
  font-weight: bold;
  /* margin-bottom: 24px; */
}

.standard-content h3 {
  font-weight: bold;
  margin-top: 12px;
  /* margin-bottom: 8px; */
  font-size: 18px;
  /* color: #1a5aad; */
}

.standard-content section {
  margin-bottom: 16px;
}

.standard-content p {
  margin: 0 0 8px 0;
  line-height: 1.8;
}
</style>
