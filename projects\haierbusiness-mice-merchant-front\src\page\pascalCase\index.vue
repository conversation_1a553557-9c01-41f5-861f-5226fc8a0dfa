<script setup lang="ts">
import { ref } from 'vue';
import GiftList from './component/giftList.vue'; //礼品
import InsuranceList from './component/insuranceList.vue'; //保险
import HotelList from './component/hotelList.vue'; //酒店
import { serviceProviderApi } from '@haierbusiness-front/apis';
import { onMounted } from 'vue';
import { MerchantTypeEnum } from '@haierbusiness-front/common-libs';
const detailData = ref<any>({});


onMounted(async () => {
  const res = await serviceProviderApi.get(undefined);
  detailData.value = res;

});
</script>

<template>
  <div style="background-color: #ffff; width: 100%; padding: 10px;">
    <GiftList v-if="detailData.merchantType === MerchantTypeEnum.GIFT" />
    <InsuranceList v-if="detailData.merchantType === MerchantTypeEnum.INSURANCE" />
    <HotelList v-if="detailData.merchantType === MerchantTypeEnum.HOTEL" />
  </div>
</template>

<style scoped lang="less"></style>
