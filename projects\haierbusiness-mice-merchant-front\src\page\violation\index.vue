<!-- 违规处理商户端 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  Modal as hModal,
  Input as hInput,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { serviceExamApi } from '@haierbusiness-front/apis';
import {
  ViolationFilter,
  TablePagination,
  ViolationRecord,
  getPunishmentStatusDesc,
  ExamineStateMap,
  ExamineStateEnum,
  AssessmentStatusEnum,
} from '@haierbusiness-front/common-libs';
import {
  PunishmentStatusMap,
  PunishmentStatusEnum,
  ViolationTypeEnum,
  ViolationTypeMap,
} from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted, h, watch } from 'vue';
import { usePagination } from 'vue-request';
import router from '../../router';
import { SearchOutlined } from '@ant-design/icons-vue';
import ViolationDetails from './violation-details.vue';
import notice from '../component/Modal.vue';
import dayjs, { Dayjs } from 'dayjs';

const currentRouter = ref();

// 表格相关数据
const searchParam = ref<ViolationFilter>({});
const {
  data,
  run: listApiRun,
  loading,
} = usePagination((params: TablePagination) =>
  serviceExamApi.getlist({
    ...searchParam.value,
    pageNum: params.current || 1,
    pageSize: params.pageSize || 10,
  }),
);

// 重置搜索条件
const reset = () => {
  searchParam.value = {};
  createTimeRange.value = undefined;
};

// 创建时间范围
const createTimeRange = ref<[Dayjs, Dayjs]>();
watch(
  () => createTimeRange.value,
  (n: any) => {
    if (n) {
      searchParam.value.againTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.againTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

const dataSource = computed(() => (data.value?.records || []) as unknown as ViolationRecord[]);
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const formatExamineState = (val: number) => {
  return PunishmentStatusMap[val as PunishmentStatusEnum] || val;
};

const detailVisible = ref(false);
const currentRecord = ref<ViolationRecord>({
  id: 0,
  examineCode: '',
  type: 0,
  score: 0,
  fine: 0,
  gmtCreate: '',
  examineState: 0,
  title: '',
  mainCode: '',
  violationDisposeEndTime: '',
  entry: '',
  details: '',
  violationDesc: '',
  violationTime: '',
  path: [],
});
const processingRecords = ref<ViolationRecord[]>([]);
const processingPagination = ref<TablePagination>({
  current: 1,
  pageSize: 10,
  total: 0,
});

const showDetailDialog = async (record: ViolationRecord) => {
  try {
    const detail = await serviceExamApi.get(record.id);
    currentRecord.value = {
      id: detail.id || 0,
      examineCode: detail.examineCode || '',
      type: Number(detail.type) || 0,
      score: detail.score || 0,
      fine: detail.fine || 0,
      gmtCreate: detail.gmtCreate || '',
      examineState: detail.examineState || 0,
      title: detail.entry || '',
      mainCode: detail.mainCode || '',
      violationDisposeEndTime: detail.violationDisposeEndTime || '',
      entry: detail.entry || '',
      details: detail.details || '',
      violationDesc: detail.violationDesc || '',
      violationTime: detail.violationTime || '',
      path: detail.path || [],
      state: detail.state || 0,
    };
    console.log('处理后的记录:', currentRecord.value);
    detailVisible.value = true;
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};

const handleDetailCancel = () => {
  detailVisible.value = false;
  currentRecord.value = {
    id: 0,
    examineCode: '',
    type: 0,
    score: 0,
    fine: 0,
    gmtCreate: '',
    examineState: 0,
    title: '',
    mainCode: '',
    violationDisposeEndTime: '',
    entry: '',
    details: '',
    violationDesc: '',
    violationTime: '',
    path: [],
    state: 0,
  };
};

const handleDetailOk = () => {
  detailVisible.value = false;
  currentRecord.value = {
    id: 0,
    examineCode: '',
    type: 0,
    score: 0,
    fine: 0,
    gmtCreate: '',
    examineState: 0,
    title: '',
    mainCode: '',
    violationDisposeEndTime: '',
    entry: '',
    details: '',
    violationDesc: '',
    violationTime: '',
    path: [],
    state: 0,
  };
  // 重新获取最新数据
  listApiRun({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    total: pagination.value.total,
  });
  console.log('已刷新最新数据');
};

const handleProcessingTableChange = (pagination: TablePagination) => {
  processingPagination.value = pagination;
};

const columns: ColumnType[] = [
  {
    title: '违规单号',
    dataIndex: 'examineCode',
    width: '250px',
    align: 'center',
  },
  {
    title: '违规类型',
    dataIndex: 'type',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return ViolationTypeMap[text as ViolationTypeEnum] || text;
    },
  },
  {
    title: '分值',
    dataIndex: 'score',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '罚款',
    dataIndex: 'fine',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text + '元',
  },
  {
    title: '考核明细',
    dataIndex: 'details',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '考核状态',
    dataIndex: 'examineState',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      const content = text !== undefined && text !== null ? ExamineStateMap[text as ExamineStateEnum] || '-' : '-';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center',
          },
        },
      };
    },
  },
  {
    title: '违规状态',
    dataIndex: 'state',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      const content = text !== undefined && text !== null ? getPunishmentStatusDesc(text) || '-' : '-';
      return {
        children: content,
        props: {
          style: {
            textAlign: 'center',
          },
        },
      };
    },
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center',
    customRender: ({ record }: { record: ViolationRecord }) => {
      if (record.state === PunishmentStatusEnum.PROCESSING || record.state === PunishmentStatusEnum.REJECTED) {
        return h(
          hButton,
          {
            type: 'link',
            onClick: () => showDetailDialog(record),
            size: 'small',
          },
          () => '处理',
        );
      } else {
        return h(
          hButton,
          {
            type: 'link',
            onClick: () => showDetailDialog(record),
            size: 'small',
          },
          () => '查看',
        );
      }
    },
  },
];

// 表格变化处理
const handleTableChange = (pag: TablePagination) => {
  listApiRun({
    current: pag.current,
    pageSize: pag.pageSize,
    total: pag.total,
  });
};


onMounted(async () => {
  currentRouter.value = await router;
  listApiRun(searchParam.value);

});
</script>

<template>
  <div class="violation-container">
    <!-- 搜索部分 -->
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="examineCode">违规单号：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.examineCode" placeholder="请输入违规单号" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="details">考核明细：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.details" placeholder="请输入考核明细" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="type">违规类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.type" allow-clear placeholder="请选择违规类型">
              <h-select-option v-for="(value, key) in ViolationTypeMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="state">违规状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select style="width: 100%" v-model:value="searchParam.state" allow-clear placeholder="请选择违规状态">
              <h-select-option v-for="(value, key) in PunishmentStatusMap" :key="key" :value="Number(key)">
                {{ value }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="createTimeRange" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="
              handleTableChange({
                current: 1,
                pageSize: 10,
                total: 0,
              })
              ">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
    </h-row>

    <!-- 表格部分 -->
    <h-row>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="(record: ViolationRecord) => record.id" :data-source="dataSource"
          :pagination="pagination" :loading="loading" @change="handleTableChange" :scroll="{ x: 1200, }">
        </h-table>
      </h-col>
    </h-row>

    <violation-details :visible="detailVisible" :data="currentRecord" :processing-records="processingRecords"
      :processing-pagination="processingPagination" @cancel="handleDetailCancel" @ok="handleDetailOk"
      @processing-table-change="handleProcessingTableChange" />
  </div>
</template>

<style scoped lang="less">
.violation-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
}
</style>