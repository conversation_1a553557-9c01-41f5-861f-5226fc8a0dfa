<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { message } from 'ant-design-vue';
import {
  CommonBox,
  Banner,
  Process,
  Consultant,
  Service,
  Resource,
  CalendarDrawer,
  InstructionModal,
  ConsultantModal,
  SatisfactionRating,
  Footer
} from './components';
import MiceBidAnchor from '@/components/MiceBidAnchor.vue';
import CalendarImg from '@/assets/image/home/<USER>';
import { usePortalStore } from './store';
import { useRoute } from 'vue-router';
import router from '../../router';
import { resolveParam } from '@haierbusiness-front/utils';
import { miceBidManOrderListApi, processOrchestrationApi } from '@haierbusiness-front/apis';

// const globalStore = applicationStore();
// const { loginUser } = storeToRefs(globalStore);
const route = useRoute();
const store = usePortalStore();
const anchorLink = ref('');

const anchorItems = [
  {
    key: '1',
    href: '#hotelProcess',
    title: '服务流程',
  },
  {
    key: '2',
    href: '#hotelConsultant',
    title: '会议顾问',
  },
  {
    key: '3',
    href: '#hotelService',
    title: '配套服务',
  },
  {
    key: '4',
    href: '#hotelResource',
    title: '直签资源',
  },
];

// 流程id
const processId = ref<string>('1');
const contentRef = ref<HTMLElement | null>(null);
const hasPermission = ref<boolean>(true);

const isQingdao = ref<string>(''); // 是否固定会议城市仅青岛
const meetingMinDaysNum = ref<number>(0); // 会议最短召开日期(单位: 天)
const processNotice = ref<string>(''); // 流程说明

const handleResize = () => {
  if (!contentRef.value) return;
  const el = contentRef.value;
  if (window.innerWidth <= 1099) {
    el.style.transform = 'scale(0.5)';
    el.style.marginTop = '-600px';
    el.style.marginLeft = '-150px';
  } else if (window.innerWidth <= 1280) {
    el.style.transform = 'scale(0.6)';
    el.style.marginTop = '-500px';
    el.style.marginLeft = '-80px';
  } else if (window.innerWidth <= 1536) {
    el.style.transform = 'scale(0.8)';
    el.style.margin = '0 auto';
    el.style.marginTop = '-237px';
  } else {
    el.style.transform = 'scale(1)';
    el.style.margin = '0 auto';
    el.style.marginTop = '0px';
  }
};

const currentRouter = ref();

const checkPermission = async () => {
  processId.value = currentRouter.value.currentRoute.query.processId; // TODO 流程id
  if (!processId.value || !(await processOrchestrationApi.checkRole(Number(processId.value)))) {
    hasPermission.value = false;
    message.error('很抱歉，您没有当前流程的使用权限！');
    return false;
  }
  return true;
};

watch(currentRouter, async () => {
  if (!(await checkPermission()) && hasPermission.value === true) {
    hasPermission.value = false;
    message.error('很抱歉，您没有当前流程的使用权限！');
  }
});

const meetingProcessDetails = (item: { isQingdao: string; meetingMinDaysNum: number; processNotice: string }) => {
  isQingdao.value = item.isQingdao;
  meetingMinDaysNum.value = item.meetingMinDaysNum;
  processNotice.value = item.processNotice;
};

//评价
const ratingVisible = ref(false)

//评价弹框
const handleok = ()=>{
  ratingVisible.value = false
}
const handlecancel = ()=>{
  ratingVisible.value = false
}

//登陆人会议列表
const meetingDetails = ref()

//获取登陆人会议列表
const meetingList = async () => {
  const res = await miceBidManOrderListApi.evaluated();

  meetingDetails.value = res
  // console.log(meetingDetails.value,"meetingDetails.value");
  if(meetingDetails.value){
    ratingVisible.value = true
  }else{
    ratingVisible.value = false
  }
}



onMounted(async () => {
  currentRouter.value = await router;
  window.addEventListener('resize', handleResize);
  handleResize(); // 初始化执行
  // 地址栏取参
  processId.value = currentRouter.value.currentRoute.query.processId; // TODO 流程id
  // 流程节点id
  localStorage.setItem('processId', processId.value);

  hasPermission.value = true;

  store.getCityList();

  if (processId.value) {
    store.getCounsellorList({ id: processId.value });
  }
  await meetingList()

  
});
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

watch(
  () => route.query.processId,
  (newProcessId, oldProcessId) => {
    if (route.path == '/index') checkPermission();
  },
);


</script>

<template>
  <!-- 商户云首页 -->
  <div class="container">
    <banner :hasPermission="hasPermission" @meetingProcessDetails="meetingProcessDetails" />
    <div class="content" ref="contentRef">
      <common-box
        id="hotelProcess"
        title="海易智会服务流程"
        desc="一对一服务，全程一单到底服务"
        :contentStyle="{ marginTop: '48px' }"
      >
        <process />
      </common-box>
      <common-box
        id="hotelConsultant"
        title="专业会议顾问"
        desc="行业内专业会议顾问，为您的每一次会议保驾"
        :contentStyle="{ marginTop: '48px' }"
      >
        <consultant />
      </common-box>
      <common-box
        id="hotelService"
        title="配套服务"
        desc="海易智会除了给您提供标准会议服务外，还提供了旅游、团建等多种服务"
        :contentStyle="{ marginTop: '48px' }"
      >
        <service />
      </common-box>
      <common-box
        id="hotelResource"
        title="直签资源"
        desc="海尔商旅会务平台直签了全国800家酒店，可享受更低的企业折扣价格"
        :contentStyle="{ marginTop: '-30px' }"
      >
        <resource />
      </common-box>
      
    </div>
    <div class="index-footer">
      <Footer/>
    </div>
    <div class="left-float" v-show="!!anchorLink">
      <mice-bid-anchor :items="anchorItems" :offsetTop="88" @change="(link: string) => anchorLink = link" />
    </div>
    <div class="right-float">
      <img :src="CalendarImg" class="calendar" @click="store.calendarDrawerOpen = true" />
    </div>
    <calendar-drawer v-model="store.calendarDrawerOpen" />
    <consultant-modal
      v-model="store.consultantModalOpen"
      :processId="processId"
      :meetingMinDaysNum="meetingMinDaysNum"
    />
    <instruction-modal v-model="store.instructionModalOpen" :processNotice="processNotice" />
    <SatisfactionRating :list="meetingDetails" :show="ratingVisible" @handleok="handleok" @cancel="handlecancel" ></SatisfactionRating>
    
  </div>
</template>

<style scoped lang="less">
:root {
  font-size: 14px;
  line-height: 1;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
.container {
  margin-top: -78px;
  .content {
    width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .left-float {
    background: #f6f9fc;
    padding: 26px 20px 26px 10px;
    border-radius: 5px;
    position: fixed;
    top: 50%;
    left: 10px;
    transform: translate(0, -50%);
    .anchor {
      .anchor-link {
        margin-bottom: 36px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  .right-float {
    position: fixed;
    /* bottom: calc(50% - 100px); */
    bottom: 200px;
    right: 10px;
    transform: translate(0, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    img.calendar {
      cursor: pointer;
      width: 31.5px;
      border-radius: 10px;
    }
  }
}
</style>
